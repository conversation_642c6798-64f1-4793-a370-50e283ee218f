import React, { useState, useEffect } from 'react';
import {
  BarChart,
  Bar,
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

const ProductPerformance = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('sessions');
  const [loading, setLoading] = useState(true);
  const [productData, setProductData] = useState([]);

  // Fetch product performance data
  useEffect(() => {
    const fetchProductData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          console.error('No authentication token found');
          return;
        }

        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/analytics/client/product-performance?timeRange=${timeRange}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setProductData(data);
        } else {
          console.error('Failed to fetch product performance data');
        }
      } catch (error) {
        console.error('Error fetching product performance:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [timeRange]);

  // Format data for charts
  const chartData = productData.map(product => ({
    name: product.productName || 'Unknown Product',
    sessions: product.sessions || 0,
    conversions: product.conversions || 0,
    conversionRate: product.conversionRate || 0,
    avgDuration: Math.round(product.avgDuration || 0),
    totalInteractions: product.totalInteractions || 0
  }));

  // Calculate metrics for display
  const totalSessions = productData.reduce((sum, product) => sum + (product.sessions || 0), 0);
  const totalConversions = productData.reduce((sum, product) => sum + (product.conversions || 0), 0);
  const totalInteractions = productData.reduce((sum, product) => sum + (product.totalInteractions || 0), 0);
  const avgConversionRate = totalSessions > 0 ? (totalConversions / totalSessions) * 100 : 0;

  // Calculate trend data for the line chart
  const trendData = productData.map(product => ({
    date: product.date || new Date().toISOString().split('T')[0],
    sessions: product.sessions || 0,
    conversions: product.conversions || 0,
    conversionRate: product.conversionRate || 0,
    avgDuration: Math.round(product.avgDuration || 0),
    totalInteractions: product.totalInteractions || 0
  }));

  const metrics = [
    { id: 'sessions', label: 'Sessions' },
    { id: 'conversions', label: 'Conversions' },
    { id: 'conversionRate', label: 'Conversion Rate' },
    { id: 'avgDuration', label: 'Avg. Duration' },
    { id: 'totalInteractions', label: 'Interactions' },
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Time Range and Metric Selector */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {metrics.map((metric) => (
            <button
              key={metric.id}
              onClick={() => setSelectedMetric(metric.id)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                selectedMetric === metric.id
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {metric.label}
            </button>
          ))}
        </div>
      </div>

      {/* Product Performance Chart */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Product Performance</h3>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey={selectedMetric} fill="#2D8C88" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Trend Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Trend Analysis</h3>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey={selectedMetric}
                stroke="#2D8C88"
                strokeWidth={2}
                dot={{ fill: '#2D8C88' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Product Details Table */}
      <div className="mt-6 bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Product Details</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conv. Rate</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Duration</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interactions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {chartData.map((product, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{product.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.sessions}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.conversions}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.conversionRate.toFixed(1)}%</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.avgDuration}s</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.totalInteractions}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ProductPerformance; 