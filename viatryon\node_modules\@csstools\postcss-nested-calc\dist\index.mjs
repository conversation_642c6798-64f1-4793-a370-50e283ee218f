import e from"postcss-value-parser";const t=/calc\(/gim;const a=a=>{const r=Object.assign({preserve:!0},a);return{postcssPlugin:"postcss-nested-calc",Declaration(a,{result:s}){if((a.value.match(t)||[]).length<2)return;if(a.variable)return;const n=a.value;let o;try{o=e(n)}catch(e){return void a.warn(s,`Failed to parse value '${n}'. Leaving the original value intact.`)}if(void 0===o)return;e.walk(o.nodes,(t=>{t.type&&"function"===t.type&&"calc"===t.value.toLowerCase()&&e.walk(t.nodes,(e=>{if(e.type&&"function"===e.type)return"calc"===e.value.toLowerCase()&&void(e.value="")}))}),!0);const c=String(o);c!==n&&(r.preserve?a.cloneBefore({value:c}):a.replaceWith(a.clone({value:c})))}}};a.postcss=!0;export{a as default};
