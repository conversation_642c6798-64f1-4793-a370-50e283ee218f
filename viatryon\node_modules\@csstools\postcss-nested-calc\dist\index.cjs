"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-value-parser"));const a=/calc\(/gim;const r=e=>{const r=Object.assign({preserve:!0},e);return{postcssPlugin:"postcss-nested-calc",Declaration(e,{result:l}){if((e.value.match(a)||[]).length<2)return;if(e.variable)return;const n=e.value;let s;try{s=t.default(n)}catch(t){return void e.warn(l,`Failed to parse value '${n}'. Leaving the original value intact.`)}if(void 0===s)return;t.default.walk(s.nodes,(e=>{e.type&&"function"===e.type&&"calc"===e.value.toLowerCase()&&t.default.walk(e.nodes,(e=>{if(e.type&&"function"===e.type)return"calc"===e.value.toLowerCase()&&void(e.value="")}))}),!0);const c=String(s);c!==n&&(r.preserve?e.cloneBefore({value:c}):e.replaceWith(e.clone({value:c})))}}};r.postcss=!0,module.exports=r;
