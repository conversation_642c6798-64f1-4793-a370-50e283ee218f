"use client";
import{useState as I,useEffect as D,useRef as f}from"react";import{toast as m}from"react-toastify";function w(n={}){let i=f(n.sort||C),s=f(n.filter||null),[d,o]=I(()=>n.data?s.current?n.data.filter(s.current).sort(i.current):[...n.data].sort(i.current):[]);return D(()=>m.onChange(t=>{if(t.status==="added"||t.status==="updated"){let e=u(t);if(s.current&&!s.current(e))return;o(a=>{let r=[],c=a.findIndex(l=>l.id===e.id);return c!==-1?(r=a.slice(),Object.assign(r[c],e,{createdAt:Date.now()})):a.length===0?r=[e]:r=[e,...a],r.sort(i.current)})}}),[]),{notifications:d,clear:()=>{o([])},markAllAsRead:(t=!0)=>{o(e=>e.map(a=>(a.read=t,a)))},markAsRead:(t,e=!0)=>{let a=r=>(r.id===t&&(r.read=e),r);Array.isArray(t)&&(a=r=>(t.includes(r.id)&&(r.read=e),r)),o(r=>r.map(a))},add:t=>{if(d.find(a=>a.id===t.id))return null;let e=u(t);return o(a=>[...a,e].sort(i.current)),e.id},update:(t,e)=>{let a=d.findIndex(r=>r.id===t);return a!==-1?(o(r=>{let c=[...r];return Object.assign(c[a],e,{createdAt:e.createdAt||Date.now()}),c.sort(i.current)}),e.id):null},remove:t=>{o(e=>e.filter(Array.isArray(t)?a=>!t.includes(a.id):a=>a.id!==t))},find:t=>Array.isArray(t)?d.filter(e=>t.includes(e.id)):d.find(e=>e.id===t),sort:t=>{i.current=t,o(e=>e.slice().sort(t))},get unreadCount(){return d.reduce((t,e)=>e.read?t:t+1,0)}}}function u(n){return n.id==null&&(n.id=Date.now().toString(36).substring(2,9)),n.createdAt||(n.createdAt=Date.now()),n.read==null&&(n.read=!1),n}function C(n,i){return i.createdAt-n.createdAt}export{u as decorate,w as useNotificationCenter};
//# sourceMappingURL=index.mjs.map