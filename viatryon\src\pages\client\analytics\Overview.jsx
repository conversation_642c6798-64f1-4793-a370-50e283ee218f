import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const Overview = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState({
    overview: {},
    trends: [],
    products: [],
    devices: []
  });

  // Fetch analytics data from API
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          console.error('No authentication token found');
          return;
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        const response = await fetch(`${apiUrl}/api/analytics/client/overview?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setAnalyticsData(data);
        } else {
          console.error('Failed to fetch analytics data:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [timeRange]);

  // Format data for charts
  const tryOnData = analyticsData.trends?.map(trend => ({
    date: trend._id,
    count: trend.sessions,
    conversions: trend.conversions || 0
  })) || [];

  const productData = analyticsData.products?.slice(0, 5).map(product => ({
    name: product.productName || 'Unknown Product',
    value: product.sessions,
    conversions: product.conversions || 0,
    avgDuration: Math.round(product.avgDuration || 0),
    interactions: product.totalInteractions || 0
  })) || [];

  const deviceData = analyticsData.devices?.map(device => ({
    name: device._id,
    value: device.sessions
  })) || [];



  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B'];

  // Format duration from seconds to readable format
  const formatDuration = (seconds) => {
    if (!seconds) return '0s';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  // Format numbers with commas
  const formatNumber = (num) => {
    if (!num) return '0';
    return num.toLocaleString();
  };

  // Calculate trend changes from previous period
  const calculateTrendChange = (current, previous) => {
    if (!previous) return { value: '0%', trend: 'neutral' };
    const change = ((current - previous) / previous) * 100;
    return {
      value: `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`,
      trend: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'
    };
  };

  const metrics = [
    {
      title: 'Total Try-Ons',
      value: formatNumber(analyticsData.overview?.totalSessions || 0),
      change: calculateTrendChange(
        analyticsData.overview?.totalSessions || 0,
        analyticsData.overview?.previousPeriod?.totalSessions || 0
      ).value,
      trend: calculateTrendChange(
        analyticsData.overview?.totalSessions || 0,
        analyticsData.overview?.previousPeriod?.totalSessions || 0
      ).trend,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      ),
    },
    {
      title: 'Average Duration',
      value: formatDuration(Math.round(analyticsData.overview?.avgDuration || 0)),
      change: calculateTrendChange(
        analyticsData.overview?.avgDuration || 0,
        analyticsData.overview?.previousPeriod?.avgDuration || 0
      ).value,
      trend: calculateTrendChange(
        analyticsData.overview?.avgDuration || 0,
        analyticsData.overview?.previousPeriod?.avgDuration || 0
      ).trend,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      title: 'Total Interactions',
      value: formatNumber(analyticsData.overview?.totalInteractions || 0),
      change: calculateTrendChange(
        analyticsData.overview?.totalInteractions || 0,
        analyticsData.overview?.previousPeriod?.totalInteractions || 0
      ).value,
      trend: calculateTrendChange(
        analyticsData.overview?.totalInteractions || 0,
        analyticsData.overview?.previousPeriod?.totalInteractions || 0
      ).trend,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 011 1v4a1 1 0 01-1 1h-4v4a1 1 0 01-1 1H8a1 1 0 01-1-1v-4H3a1 1 0 01-1-1V5a1 1 0 011-1h4z" />
        </svg>
      ),
    },
    {
      title: 'Unique Users',
      value: formatNumber(analyticsData.overview?.uniqueUsers || 0),
      change: calculateTrendChange(
        analyticsData.overview?.uniqueUsers || 0,
        analyticsData.overview?.previousPeriod?.uniqueUsers || 0
      ).value,
      trend: calculateTrendChange(
        analyticsData.overview?.uniqueUsers || 0,
        analyticsData.overview?.previousPeriod?.uniqueUsers || 0
      ).trend,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="p-4 md:p-6 space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {metrics.map((metric) => (
          <div key={metric.title} className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                {metric.icon}
              </div>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Try-On Trends */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Try-On Trends</h3>
          <div className="h-64 md:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={tryOnData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="count"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Products</h3>
          <div className="h-64 md:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={productData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#2D8C88" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Device Distribution */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Device Distribution</h3>
          <div className="h-64 md:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>


      </div>
    </div>
  );
};

export default Overview; 