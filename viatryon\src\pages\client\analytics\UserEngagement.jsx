import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const UserEngagement = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [engagementData, setEngagementData] = useState({
    engagement: {},
    hourlyDistribution: [],
    interactionTypes: [],
    dailyMetrics: []
  });

  // Fetch user engagement data
  useEffect(() => {
    const fetchEngagementData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          console.error('No authentication token found');
          return;
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        const response = await fetch(`${apiUrl}/api/analytics/client/user-engagement?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setEngagementData(data);
        } else {
          console.error('Failed to fetch engagement data:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error fetching engagement data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEngagementData();
  }, [timeRange]);

  // Format data for charts
  const hourlyData = engagementData.hourlyDistribution?.map(item => ({
    hour: item._id.toString().padStart(2, '0'),
    sessions: item.sessions
  })) || [];

  const interactionTypesData = engagementData.interactionTypes?.map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  })) || [];

  // Format daily metrics for the table
  const dailyMetrics = engagementData.dailyMetrics?.map(item => ({
    date: item._id,
    sessions: item.sessions || 0,
    avgDuration: Math.round(item.avgDuration || 0),
    interactions: item.interactions || 0
  })) || [];

  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  // Calculate trend changes from previous period
  const calculateTrendChange = (current, previous) => {
    if (!previous) return { value: '0%', trend: 'neutral' };
    const change = ((current - previous) / previous) * 100;
    return {
      value: `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`,
      trend: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'
    };
  };

  // Format engagement metrics
  const formatDuration = (seconds) => {
    if (!seconds) return '0s';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  const formatNumber = (num) => {
    if (!num) return '0';
    return num.toLocaleString();
  };

  const metrics = [
    {
      title: 'Total Sessions',
      value: formatNumber(engagementData.engagement?.totalSessions || 0),
      change: calculateTrendChange(
        engagementData.engagement?.totalSessions || 0,
        engagementData.engagement?.previousPeriod?.totalSessions || 0
      ).value,
      trend: calculateTrendChange(
        engagementData.engagement?.totalSessions || 0,
        engagementData.engagement?.previousPeriod?.totalSessions || 0
      ).trend,
    },
    {
      title: 'Avg. Session Duration',
      value: formatDuration(engagementData.engagement?.avgDuration || 0),
      change: calculateTrendChange(
        engagementData.engagement?.avgDuration || 0,
        engagementData.engagement?.previousPeriod?.avgDuration || 0
      ).value,
      trend: calculateTrendChange(
        engagementData.engagement?.avgDuration || 0,
        engagementData.engagement?.previousPeriod?.avgDuration || 0
      ).trend,
    },
    {
      title: 'Total Interactions',
      value: formatNumber(engagementData.engagement?.totalInteractions || 0),
      change: calculateTrendChange(
        engagementData.engagement?.totalInteractions || 0,
        engagementData.engagement?.previousPeriod?.totalInteractions || 0
      ).value,
      trend: calculateTrendChange(
        engagementData.engagement?.totalInteractions || 0,
        engagementData.engagement?.previousPeriod?.totalInteractions || 0
      ).trend,
    },
    {
      title: 'Avg. Interactions/Session',
      value: (engagementData.engagement?.avgInteractionsPerSession || 0).toFixed(2),
      change: calculateTrendChange(
        engagementData.engagement?.avgInteractionsPerSession || 0,
        engagementData.engagement?.previousPeriod?.avgInteractionsPerSession || 0
      ).value,
      trend: calculateTrendChange(
        engagementData.engagement?.avgInteractionsPerSession || 0,
        engagementData.engagement?.previousPeriod?.avgInteractionsPerSession || 0
      ).trend,
    },
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric) => (
          <div key={metric.title} className="bg-white rounded-xl shadow-sm p-6">
            <div>
              <p className="text-sm font-medium text-gray-600">{metric.title}</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Engagement Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={hourlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sessions" fill="#2D8C88" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Interaction Types</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={interactionTypesData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {interactionTypesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Detailed Metrics</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Duration</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interactions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {dailyMetrics.map((day) => (
                <tr key={day.date} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{day.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.sessions}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDuration(day.avgDuration)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.interactions}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default UserEngagement; 