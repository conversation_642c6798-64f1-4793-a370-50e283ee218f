'use strict';

/* eslint global-require: 0 */
// https://262.ecma-international.org/16.0/#sec-abstract-operations
var ES2025 = {
	abs: require('./2025/abs'),
	AddEntriesFromIterable: require('./2025/AddEntriesFromIterable'),
	AddToKeptObjects: require('./2025/AddToKeptObjects'),
	AddValueToKeyedGroup: require('./2025/AddValueToKeyedGroup'),
	AdvanceStringIndex: require('./2025/AdvanceStringIndex'),
	AllCharacters: require('./2025/AllCharacters'),
	ApplyStringOrNumericBinaryOperator: require('./2025/ApplyStringOrNumericBinaryOperator'),
	ArrayBufferByteLength: require('./2025/ArrayBufferByteLength'),
	ArrayBufferCopyAndDetach: require('./2025/ArrayBufferCopyAndDetach'),
	ArrayCreate: require('./2025/ArrayCreate'),
	ArraySetLength: require('./2025/ArraySetLength'),
	ArraySpeciesCreate: require('./2025/ArraySpeciesCreate'),
	AsyncFromSyncIteratorContinuation: require('./2025/AsyncFromSyncIteratorContinuation'),
	AsyncIteratorClose: require('./2025/AsyncIteratorClose'),
	BigInt: require('./2025/BigInt'),
	BigIntBitwiseOp: require('./2025/BigIntBitwiseOp'),
	BinaryAnd: require('./2025/BinaryAnd'),
	BinaryOr: require('./2025/BinaryOr'),
	BinaryXor: require('./2025/BinaryXor'),
	ByteListBitwiseOp: require('./2025/ByteListBitwiseOp'),
	ByteListEqual: require('./2025/ByteListEqual'),
	Call: require('./2025/Call'),
	CanBeHeldWeakly: require('./2025/CanBeHeldWeakly'),
	Canonicalize: require('./2025/Canonicalize'),
	CanonicalizeKeyedCollectionKey: require('./2025/CanonicalizeKeyedCollectionKey'),
	CanonicalNumericIndexString: require('./2025/CanonicalNumericIndexString'),
	CharacterComplement: require('./2025/CharacterComplement'),
	CharacterRange: require('./2025/CharacterRange'),
	clamp: require('./2025/clamp'),
	ClearKeptObjects: require('./2025/ClearKeptObjects'),
	CloneArrayBuffer: require('./2025/CloneArrayBuffer'),
	CodePointAt: require('./2025/CodePointAt'),
	CodePointsToString: require('./2025/CodePointsToString'),
	CompareArrayElements: require('./2025/CompareArrayElements'),
	CompareTypedArrayElements: require('./2025/CompareTypedArrayElements'),
	CompletePropertyDescriptor: require('./2025/CompletePropertyDescriptor'),
	CompletionRecord: require('./2025/CompletionRecord'),
	CopyDataProperties: require('./2025/CopyDataProperties'),
	CreateAsyncFromSyncIterator: require('./2025/CreateAsyncFromSyncIterator'),
	CreateDataProperty: require('./2025/CreateDataProperty'),
	CreateDataPropertyOrThrow: require('./2025/CreateDataPropertyOrThrow'),
	CreateHTML: require('./2025/CreateHTML'),
	CreateIteratorFromClosure: require('./2025/CreateIteratorFromClosure'),
	CreateIteratorResultObject: require('./2025/CreateIteratorResultObject'),
	CreateListFromArrayLike: require('./2025/CreateListFromArrayLike'),
	CreateNonEnumerableDataPropertyOrThrow: require('./2025/CreateNonEnumerableDataPropertyOrThrow'),
	CreateRegExpStringIterator: require('./2025/CreateRegExpStringIterator'),
	DateFromTime: require('./2025/DateFromTime'),
	DateString: require('./2025/DateString'),
	Day: require('./2025/Day'),
	DayFromYear: require('./2025/DayFromYear'),
	DaysInYear: require('./2025/DaysInYear'),
	DayWithinYear: require('./2025/DayWithinYear'),
	DefineMethodProperty: require('./2025/DefineMethodProperty'),
	DefinePropertyOrThrow: require('./2025/DefinePropertyOrThrow'),
	DeletePropertyOrThrow: require('./2025/DeletePropertyOrThrow'),
	DetachArrayBuffer: require('./2025/DetachArrayBuffer'),
	EncodeForRegExpEscape: require('./2025/EncodeForRegExpEscape'),
	EnumerableOwnProperties: require('./2025/EnumerableOwnProperties'),
	FindViaPredicate: require('./2025/FindViaPredicate'),
	FlattenIntoArray: require('./2025/FlattenIntoArray'),
	floor: require('./2025/floor'),
	FromPropertyDescriptor: require('./2025/FromPropertyDescriptor'),
	GeneratorResume: require('./2025/GeneratorResume'),
	GeneratorResumeAbrupt: require('./2025/GeneratorResumeAbrupt'),
	GeneratorStart: require('./2025/GeneratorStart'),
	GeneratorValidate: require('./2025/GeneratorValidate'),
	Get: require('./2025/Get'),
	GetArrayBufferMaxByteLengthOption: require('./2025/GetArrayBufferMaxByteLengthOption'),
	GetGlobalObject: require('./2025/GetGlobalObject'),
	GetIterator: require('./2025/GetIterator'),
	GetIteratorDirect: require('./2025/GetIteratorDirect'),
	GetIteratorFlattenable: require('./2025/GetIteratorFlattenable'),
	GetIteratorFromMethod: require('./2025/GetIteratorFromMethod'),
	GetMatchIndexPair: require('./2025/GetMatchIndexPair'),
	GetMatchString: require('./2025/GetMatchString'),
	GetMethod: require('./2025/GetMethod'),
	GetNamedTimeZoneEpochNanoseconds: require('./2025/GetNamedTimeZoneEpochNanoseconds'),
	GetOwnPropertyKeys: require('./2025/GetOwnPropertyKeys'),
	GetPromiseResolve: require('./2025/GetPromiseResolve'),
	GetPrototypeFromConstructor: require('./2025/GetPrototypeFromConstructor'),
	GetSetRecord: require('./2025/GetSetRecord'),
	GetStringIndex: require('./2025/GetStringIndex'),
	GetSubstitution: require('./2025/GetSubstitution'),
	GetUTCEpochNanoseconds: require('./2025/GetUTCEpochNanoseconds'),
	GetV: require('./2025/GetV'),
	GetValueFromBuffer: require('./2025/GetValueFromBuffer'),
	GetViewByteLength: require('./2025/GetViewByteLength'),
	GroupBy: require('./2025/GroupBy'),
	HasEitherUnicodeFlag: require('./2025/HasEitherUnicodeFlag'),
	HasOwnProperty: require('./2025/HasOwnProperty'),
	HasProperty: require('./2025/HasProperty'),
	HourFromTime: require('./2025/HourFromTime'),
	IfAbruptCloseIterator: require('./2025/IfAbruptCloseIterator'),
	InLeapYear: require('./2025/InLeapYear'),
	InstallErrorCause: require('./2025/InstallErrorCause'),
	InstanceofOperator: require('./2025/InstanceofOperator'),
	InternalizeJSONProperty: require('./2025/InternalizeJSONProperty'),
	Invoke: require('./2025/Invoke'),
	IsAccessorDescriptor: require('./2025/IsAccessorDescriptor'),
	IsArray: require('./2025/IsArray'),
	IsArrayBufferViewOutOfBounds: require('./2025/IsArrayBufferViewOutOfBounds'),
	IsBigIntElementType: require('./2025/IsBigIntElementType'),
	IsCallable: require('./2025/IsCallable'),
	IsCompatiblePropertyDescriptor: require('./2025/IsCompatiblePropertyDescriptor'),
	IsConcatSpreadable: require('./2025/IsConcatSpreadable'),
	IsConstructor: require('./2025/IsConstructor'),
	IsDataDescriptor: require('./2025/IsDataDescriptor'),
	IsDetachedBuffer: require('./2025/IsDetachedBuffer'),
	IsExtensible: require('./2025/IsExtensible'),
	IsFixedLengthArrayBuffer: require('./2025/IsFixedLengthArrayBuffer'),
	IsGenericDescriptor: require('./2025/IsGenericDescriptor'),
	IsLessThan: require('./2025/IsLessThan'),
	IsLooselyEqual: require('./2025/IsLooselyEqual'),
	IsNoTearConfiguration: require('./2025/IsNoTearConfiguration'),
	IsPromise: require('./2025/IsPromise'),
	IsRegExp: require('./2025/IsRegExp'),
	IsSharedArrayBuffer: require('./2025/IsSharedArrayBuffer'),
	IsStrictlyEqual: require('./2025/IsStrictlyEqual'),
	IsStringWellFormedUnicode: require('./2025/IsStringWellFormedUnicode'),
	IsTimeZoneOffsetString: require('./2025/IsTimeZoneOffsetString'),
	IsTypedArrayFixedLength: require('./2025/IsTypedArrayFixedLength'),
	IsTypedArrayOutOfBounds: require('./2025/IsTypedArrayOutOfBounds'),
	IsUnclampedIntegerElementType: require('./2025/IsUnclampedIntegerElementType'),
	IsUnsignedElementType: require('./2025/IsUnsignedElementType'),
	IsValidIntegerIndex: require('./2025/IsValidIntegerIndex'),
	IsViewOutOfBounds: require('./2025/IsViewOutOfBounds'),
	IsWordChar: require('./2025/IsWordChar'),
	IteratorClose: require('./2025/IteratorClose'),
	IteratorComplete: require('./2025/IteratorComplete'),
	IteratorNext: require('./2025/IteratorNext'),
	IteratorStep: require('./2025/IteratorStep'),
	IteratorStepValue: require('./2025/IteratorStepValue'),
	IteratorToList: require('./2025/IteratorToList'),
	IteratorValue: require('./2025/IteratorValue'),
	KeyForSymbol: require('./2025/KeyForSymbol'),
	LengthOfArrayLike: require('./2025/LengthOfArrayLike'),
	MakeDataViewWithBufferWitnessRecord: require('./2025/MakeDataViewWithBufferWitnessRecord'),
	MakeDate: require('./2025/MakeDate'),
	MakeDay: require('./2025/MakeDay'),
	MakeFullYear: require('./2025/MakeFullYear'),
	MakeMatchIndicesIndexPairArray: require('./2025/MakeMatchIndicesIndexPairArray'),
	MakeTime: require('./2025/MakeTime'),
	MakeTypedArrayWithBufferWitnessRecord: require('./2025/MakeTypedArrayWithBufferWitnessRecord'),
	max: require('./2025/max'),
	min: require('./2025/min'),
	MinFromTime: require('./2025/MinFromTime'),
	modulo: require('./2025/modulo'),
	MonthFromTime: require('./2025/MonthFromTime'),
	msFromTime: require('./2025/msFromTime'),
	NewPromiseCapability: require('./2025/NewPromiseCapability'),
	NormalCompletion: require('./2025/NormalCompletion'),
	Number: require('./2025/Number'),
	NumberBitwiseOp: require('./2025/NumberBitwiseOp'),
	NumberToBigInt: require('./2025/NumberToBigInt'),
	NumericToRawBytes: require('./2025/NumericToRawBytes'),
	ObjectDefineProperties: require('./2025/ObjectDefineProperties'),
	OrdinaryCreateFromConstructor: require('./2025/OrdinaryCreateFromConstructor'),
	OrdinaryDefineOwnProperty: require('./2025/OrdinaryDefineOwnProperty'),
	OrdinaryGetOwnProperty: require('./2025/OrdinaryGetOwnProperty'),
	OrdinaryGetPrototypeOf: require('./2025/OrdinaryGetPrototypeOf'),
	OrdinaryHasInstance: require('./2025/OrdinaryHasInstance'),
	OrdinaryHasProperty: require('./2025/OrdinaryHasProperty'),
	OrdinaryObjectCreate: require('./2025/OrdinaryObjectCreate'),
	OrdinarySetPrototypeOf: require('./2025/OrdinarySetPrototypeOf'),
	OrdinaryToPrimitive: require('./2025/OrdinaryToPrimitive'),
	ParseHexOctet: require('./2025/ParseHexOctet'),
	PromiseResolve: require('./2025/PromiseResolve'),
	QuoteJSONString: require('./2025/QuoteJSONString'),
	RawBytesToNumeric: require('./2025/RawBytesToNumeric'),
	RegExpCreate: require('./2025/RegExpCreate'),
	RegExpExec: require('./2025/RegExpExec'),
	RegExpHasFlag: require('./2025/RegExpHasFlag'),
	RequireObjectCoercible: require('./2025/RequireObjectCoercible'),
	ReturnCompletion: require('./2025/ReturnCompletion'),
	SameType: require('./2025/SameType'),
	SameValue: require('./2025/SameValue'),
	SameValueNonNumber: require('./2025/SameValueNonNumber'),
	SameValueZero: require('./2025/SameValueZero'),
	SecFromTime: require('./2025/SecFromTime'),
	Set: require('./2025/Set'),
	SetDataHas: require('./2025/SetDataHas'),
	SetDataIndex: require('./2025/SetDataIndex'),
	SetDataSize: require('./2025/SetDataSize'),
	SetFunctionLength: require('./2025/SetFunctionLength'),
	SetFunctionName: require('./2025/SetFunctionName'),
	SetIntegrityLevel: require('./2025/SetIntegrityLevel'),
	SetterThatIgnoresPrototypeProperties: require('./2025/SetterThatIgnoresPrototypeProperties'),
	SetTypedArrayFromArrayLike: require('./2025/SetTypedArrayFromArrayLike'),
	SetTypedArrayFromTypedArray: require('./2025/SetTypedArrayFromTypedArray'),
	SetValueInBuffer: require('./2025/SetValueInBuffer'),
	SortIndexedProperties: require('./2025/SortIndexedProperties'),
	SpeciesConstructor: require('./2025/SpeciesConstructor'),
	StringCreate: require('./2025/StringCreate'),
	StringGetOwnProperty: require('./2025/StringGetOwnProperty'),
	StringIndexOf: require('./2025/StringIndexOf'),
	StringLastIndexOf: require('./2025/StringLastIndexOf'),
	StringPad: require('./2025/StringPad'),
	StringPaddingBuiltinsImpl: require('./2025/StringPaddingBuiltinsImpl'),
	StringToBigInt: require('./2025/StringToBigInt'),
	StringToCodePoints: require('./2025/StringToCodePoints'),
	StringToNumber: require('./2025/StringToNumber'),
	substring: require('./2025/substring'),
	SymbolDescriptiveString: require('./2025/SymbolDescriptiveString'),
	SystemTimeZoneIdentifier: require('./2025/SystemTimeZoneIdentifier'),
	TestIntegrityLevel: require('./2025/TestIntegrityLevel'),
	ThisBigIntValue: require('./2025/ThisBigIntValue'),
	ThisBooleanValue: require('./2025/ThisBooleanValue'),
	ThisNumberValue: require('./2025/ThisNumberValue'),
	ThisStringValue: require('./2025/ThisStringValue'),
	ThisSymbolValue: require('./2025/ThisSymbolValue'),
	ThrowCompletion: require('./2025/ThrowCompletion'),
	TimeClip: require('./2025/TimeClip'),
	TimeFromYear: require('./2025/TimeFromYear'),
	TimeString: require('./2025/TimeString'),
	TimeWithinDay: require('./2025/TimeWithinDay'),
	TimeZoneString: require('./2025/TimeZoneString'),
	ToBigInt: require('./2025/ToBigInt'),
	ToBigInt64: require('./2025/ToBigInt64'),
	ToBigUint64: require('./2025/ToBigUint64'),
	ToBoolean: require('./2025/ToBoolean'),
	ToDateString: require('./2025/ToDateString'),
	ToIndex: require('./2025/ToIndex'),
	ToInt16: require('./2025/ToInt16'),
	ToInt32: require('./2025/ToInt32'),
	ToInt8: require('./2025/ToInt8'),
	ToIntegerOrInfinity: require('./2025/ToIntegerOrInfinity'),
	ToLength: require('./2025/ToLength'),
	ToNumber: require('./2025/ToNumber'),
	ToNumeric: require('./2025/ToNumeric'),
	ToObject: require('./2025/ToObject'),
	ToPrimitive: require('./2025/ToPrimitive'),
	ToPropertyDescriptor: require('./2025/ToPropertyDescriptor'),
	ToPropertyKey: require('./2025/ToPropertyKey'),
	ToString: require('./2025/ToString'),
	ToUint16: require('./2025/ToUint16'),
	ToUint32: require('./2025/ToUint32'),
	ToUint8: require('./2025/ToUint8'),
	ToUint8Clamp: require('./2025/ToUint8Clamp'),
	ToZeroPaddedDecimalString: require('./2025/ToZeroPaddedDecimalString'),
	TrimString: require('./2025/TrimString'),
	truncate: require('./2025/truncate'),
	TypedArrayByteLength: require('./2025/TypedArrayByteLength'),
	TypedArrayCreateFromConstructor: require('./2025/TypedArrayCreateFromConstructor'),
	TypedArrayCreateSameType: require('./2025/TypedArrayCreateSameType'),
	TypedArrayElementSize: require('./2025/TypedArrayElementSize'),
	TypedArrayElementType: require('./2025/TypedArrayElementType'),
	TypedArrayGetElement: require('./2025/TypedArrayGetElement'),
	TypedArrayLength: require('./2025/TypedArrayLength'),
	TypedArraySetElement: require('./2025/TypedArraySetElement'),
	TypedArraySpeciesCreate: require('./2025/TypedArraySpeciesCreate'),
	UnicodeEscape: require('./2025/UnicodeEscape'),
	UpdateModifiers: require('./2025/UpdateModifiers'),
	UTF16EncodeCodePoint: require('./2025/UTF16EncodeCodePoint'),
	UTF16SurrogatePairToCodePoint: require('./2025/UTF16SurrogatePairToCodePoint'),
	ValidateAndApplyPropertyDescriptor: require('./2025/ValidateAndApplyPropertyDescriptor'),
	ValidateAtomicAccess: require('./2025/ValidateAtomicAccess'),
	ValidateAtomicAccessOnIntegerTypedArray: require('./2025/ValidateAtomicAccessOnIntegerTypedArray'),
	ValidateIntegerTypedArray: require('./2025/ValidateIntegerTypedArray'),
	ValidateTypedArray: require('./2025/ValidateTypedArray'),
	WeakRefDeref: require('./2025/WeakRefDeref'),
	WeekDay: require('./2025/WeekDay'),
	WordCharacters: require('./2025/WordCharacters'),
	YearFromTime: require('./2025/YearFromTime')
};

module.exports = ES2025;
