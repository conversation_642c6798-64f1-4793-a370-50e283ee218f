import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const ConversionRates = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [conversionData, setConversionData] = useState({
    funnel: {},
    categoryConversions: []
  });

  // Fetch conversion data
  useEffect(() => {
    const fetchConversionData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          console.error('No authentication token found');
          return;
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        const response = await fetch(`${apiUrl}/api/analytics/client/conversion-rates?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setConversionData(data);
        } else {
          console.error('Failed to fetch conversion data');
        }
      } catch (error) {
        console.error('Error fetching conversion data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConversionData();
  }, [timeRange]);

  // Format data for charts
  const funnelData = [
    {
      stage: 'Total Sessions',
      value: conversionData.funnel?.totalSessions || 0,
      rate: '100%'
    },
    {
      stage: 'Completed Sessions',
      value: conversionData.funnel?.completedSessions || 0,
      rate: `${((conversionData.funnel?.completedSessions || 0) / (conversionData.funnel?.totalSessions || 1) * 100).toFixed(1)}%`
    },
    {
      stage: 'Shared Sessions',
      value: conversionData.funnel?.sharedSessions || 0,
      rate: `${((conversionData.funnel?.sharedSessions || 0) / (conversionData.funnel?.totalSessions || 1) * 100).toFixed(1)}%`
    },
    {
      stage: 'Purchased Sessions',
      value: conversionData.funnel?.convertedSessions || 0,
      rate: `${((conversionData.funnel?.convertedSessions || 0) / (conversionData.funnel?.totalSessions || 1) * 100).toFixed(1)}%`
    },
  ];

  const categoryData = conversionData.categoryConversions?.map(category => ({
    name: category._id || 'Unknown',
    sessions: category.sessions,
    conversions: category.conversions,
    conversionRate: category.conversionRate
  })) || [];

  const formatNumber = (num) => {
    if (!num) return '0';
    return num.toLocaleString();
  };

  const formatCurrency = (amount) => {
    if (!amount) return '$0.00';
    return `$${amount.toFixed(2)}`;
  };

  const overallConversionRate = (conversionData.funnel?.convertedSessions || 0) / (conversionData.funnel?.totalSessions || 1) * 100;

  const completionRate = ((conversionData.funnel?.completedSessions || 0) / (conversionData.funnel?.totalSessions || 1) * 100);
  const shareRate = ((conversionData.funnel?.sharedSessions || 0) / (conversionData.funnel?.totalSessions || 1) * 100);

  const metrics = [
    {
      title: 'Total Sessions',
      value: formatNumber(conversionData.funnel?.totalSessions || 0),
      change: '+0%', // TODO: Calculate from previous period
      trend: 'neutral',
    },
    {
      title: 'Completed Sessions',
      value: formatNumber(conversionData.funnel?.completedSessions || 0),
      change: '+0%', // TODO: Calculate from previous period
      trend: 'neutral',
    },
    {
      title: 'Shared Sessions',
      value: formatNumber(conversionData.funnel?.sharedSessions || 0),
      change: '+0%', // TODO: Calculate from previous period
      trend: 'neutral',
    },
    {
      title: 'Completion Rate',
      value: `${completionRate.toFixed(1)}%`,
      change: '+0%', // TODO: Calculate from previous period
      trend: 'neutral',
    },
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric) => (
          <div key={metric.title} className="bg-white rounded-xl shadow-sm p-6">
            <div>
              <p className="text-sm font-medium text-gray-600">{metric.title}</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Conversion Funnel */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Conversion Funnel</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={funnelData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="stage" type="category" />
              <Tooltip />
              <Bar dataKey="value" fill="#2D8C88" />
            </BarChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 grid grid-cols-4 gap-4">
          {funnelData.map((stage) => (
            <div key={stage.stage} className="text-center">
              <p className="text-sm font-medium text-gray-600">{stage.stage}</p>
              <p className="text-lg font-semibold text-gray-900">{stage.value}</p>
              <p className="text-sm text-gray-500">{stage.rate}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Category Performance */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Performance by Category</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={categoryData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="sessions" fill="#2D8C88" name="Sessions" />
              <Bar dataKey="conversions" fill="#10B981" name="Conversions" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ConversionRates; 