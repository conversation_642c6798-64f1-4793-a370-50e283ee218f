"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=e(require("postcss-value-parser"));const l=e=>{const l=Object.assign({preserve:!1},e);return{postcssPlugin:"postcss-text-decoration-shorthand",prepare(){const e=new Map;return{OnceExit:()=>{e.clear()},Declaration:i=>{if("text-decoration"!==i.prop.toLowerCase())return;const s=i.parent.index(i);if(i.parent.nodes.some((r=>"decl"===r.type&&"text-decoration"===r.prop.toLowerCase()&&e.get(i.value)===r.value&&i.parent.index(r)!==s)))return;const u=r.default(i.value).nodes.filter((e=>"space"!==e.type&&"comment"!==e.type));if(u.length>4)return;if(u.find((e=>"var"===e.value.toLowerCase()&&"function"===e.type)))return;if(u.find((e=>"word"===e.type&&o.includes(e.value))))return;const d={line:null,style:null,color:null,thickness:null};for(let e=0;e<u.length;e++){const r=u[e];"word"===r.type&&a.includes(r.value.toLowerCase())?d.line=r:"word"===r.type&&n.includes(r.value.toLowerCase())?d.style=r:t(r)?d.color=r:"word"!==r.type||"none"!==r.value.toLowerCase()?d.thickness=r:(d.color||(d.color=r),d.line||(d.line=r))}d.line||(d.line={type:"word",value:"none"}),d.style||(d.style={type:"word",value:"solid"}),d.color||(d.color={type:"word",value:"currentColor"});try{const e=r.default.unit(d.thickness.value);e&&"%"===e.unit&&(d.thickness={type:"function",value:"calc",nodes:[{type:"word",value:"0.01em"},{type:"space",value:" "},{type:"word",value:"*"},{type:"space",value:" "},{type:"word",value:e.number}]})}catch(e){}const c=r.default.stringify(d.line);if(i.value.toLowerCase()===c)return;const p=r.default.stringify([d.line,{type:"space",value:" "},d.style,{type:"space",value:" "},d.color]);i.cloneBefore({prop:"text-decoration",value:c}),(d.thickness||3!==u.length)&&i.cloneBefore({prop:"text-decoration",value:p}),d.thickness&&i.cloneBefore({prop:"text-decoration-thickness",value:r.default.stringify([d.thickness])}),e.set(i.value,c),e.set(p,c),l.preserve||i.remove()}}}}};function t(e){return!("word"!==e.type||!e.value.startsWith("#"))||(!("word"!==e.type||!s.includes(e.value.toLowerCase()))||!("function"!==e.type||!i.includes(e.value.toLowerCase())))}l.postcss=!0;const o=["unset","inherit","initial","revert","revert-layer"],a=["underline","overline","line-through","blink","spelling-error","grammar-error"],n=["solid","double","dotted","dashed","wavy"],i=["rgb","rgba","hsl","hsla","hwb","lch","lab","color","oklch","oklab"],s=["currentcolor","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"];module.exports=l;
