'use strict';

/* eslint global-require: 0 */
// https://262.ecma-international.org/9.0/#sec-abstract-operations
var ES2018 = {
	'Abstract Equality Comparison': require('./2018/AbstractEqualityComparison'),
	'Abstract Relational Comparison': require('./2018/AbstractRelationalComparison'),
	'Strict Equality Comparison': require('./2018/StrictEqualityComparison'),
	abs: require('./2018/abs'),
	AdvanceStringIndex: require('./2018/AdvanceStringIndex'),
	ArrayCreate: require('./2018/ArrayCreate'),
	ArraySetLength: require('./2018/ArraySetLength'),
	ArraySpeciesCreate: require('./2018/ArraySpeciesCreate'),
	AsyncIteratorClose: require('./2018/AsyncIteratorClose'),
	Call: require('./2018/Call'),
	Canonicalize: require('./2018/Canonicalize'),
	CanonicalNumericIndexString: require('./2018/CanonicalNumericIndexString'),
	CharacterRange: require('./2018/CharacterRange'),
	CompletePropertyDescriptor: require('./2018/CompletePropertyDescriptor'),
	CompletionRecord: require('./2018/CompletionRecord'),
	CopyDataProperties: require('./2018/CopyDataProperties'),
	CreateAsyncFromSyncIterator: require('./2018/CreateAsyncFromSyncIterator'),
	CreateDataProperty: require('./2018/CreateDataProperty'),
	CreateDataPropertyOrThrow: require('./2018/CreateDataPropertyOrThrow'),
	CreateHTML: require('./2018/CreateHTML'),
	CreateIterResultObject: require('./2018/CreateIterResultObject'),
	CreateListFromArrayLike: require('./2018/CreateListFromArrayLike'),
	CreateMethodProperty: require('./2018/CreateMethodProperty'),
	DateFromTime: require('./2018/DateFromTime'),
	DateString: require('./2018/DateString'),
	Day: require('./2018/Day'),
	DayFromYear: require('./2018/DayFromYear'),
	DaysInYear: require('./2018/DaysInYear'),
	DayWithinYear: require('./2018/DayWithinYear'),
	DefinePropertyOrThrow: require('./2018/DefinePropertyOrThrow'),
	DeletePropertyOrThrow: require('./2018/DeletePropertyOrThrow'),
	DetachArrayBuffer: require('./2018/DetachArrayBuffer'),
	EnumerableOwnPropertyNames: require('./2018/EnumerableOwnPropertyNames'),
	floor: require('./2018/floor'),
	FromPropertyDescriptor: require('./2018/FromPropertyDescriptor'),
	Get: require('./2018/Get'),
	GetGlobalObject: require('./2018/GetGlobalObject'),
	GetIterator: require('./2018/GetIterator'),
	GetMethod: require('./2018/GetMethod'),
	GetOwnPropertyKeys: require('./2018/GetOwnPropertyKeys'),
	GetPrototypeFromConstructor: require('./2018/GetPrototypeFromConstructor'),
	GetSubstitution: require('./2018/GetSubstitution'),
	GetV: require('./2018/GetV'),
	GetValueFromBuffer: require('./2018/GetValueFromBuffer'),
	HasOwnProperty: require('./2018/HasOwnProperty'),
	HasProperty: require('./2018/HasProperty'),
	HourFromTime: require('./2018/HourFromTime'),
	InLeapYear: require('./2018/InLeapYear'),
	InstanceofOperator: require('./2018/InstanceofOperator'),
	IntegerIndexedElementGet: require('./2018/IntegerIndexedElementGet'),
	IntegerIndexedElementSet: require('./2018/IntegerIndexedElementSet'),
	InternalizeJSONProperty: require('./2018/InternalizeJSONProperty'),
	Invoke: require('./2018/Invoke'),
	IsAccessorDescriptor: require('./2018/IsAccessorDescriptor'),
	IsArray: require('./2018/IsArray'),
	IsCallable: require('./2018/IsCallable'),
	IsCompatiblePropertyDescriptor: require('./2018/IsCompatiblePropertyDescriptor'),
	IsConcatSpreadable: require('./2018/IsConcatSpreadable'),
	IsConstructor: require('./2018/IsConstructor'),
	IsDataDescriptor: require('./2018/IsDataDescriptor'),
	IsDetachedBuffer: require('./2018/IsDetachedBuffer'),
	IsExtensible: require('./2018/IsExtensible'),
	IsGenericDescriptor: require('./2018/IsGenericDescriptor'),
	IsInteger: require('./2018/IsInteger'),
	IsPromise: require('./2018/IsPromise'),
	IsPropertyKey: require('./2018/IsPropertyKey'),
	IsRegExp: require('./2018/IsRegExp'),
	IsSharedArrayBuffer: require('./2018/IsSharedArrayBuffer'),
	IsStringPrefix: require('./2018/IsStringPrefix'),
	IsWordChar: require('./2018/IsWordChar'),
	IterableToList: require('./2018/IterableToList'),
	IteratorClose: require('./2018/IteratorClose'),
	IteratorComplete: require('./2018/IteratorComplete'),
	IteratorNext: require('./2018/IteratorNext'),
	IteratorStep: require('./2018/IteratorStep'),
	IteratorValue: require('./2018/IteratorValue'),
	MakeDate: require('./2018/MakeDate'),
	MakeDay: require('./2018/MakeDay'),
	MakeTime: require('./2018/MakeTime'),
	max: require('./2018/max'),
	min: require('./2018/min'),
	MinFromTime: require('./2018/MinFromTime'),
	modulo: require('./2018/modulo'),
	MonthFromTime: require('./2018/MonthFromTime'),
	msFromTime: require('./2018/msFromTime'),
	NewPromiseCapability: require('./2018/NewPromiseCapability'),
	NormalCompletion: require('./2018/NormalCompletion'),
	NumberToRawBytes: require('./2018/NumberToRawBytes'),
	NumberToString: require('./2018/NumberToString'),
	ObjectCreate: require('./2018/ObjectCreate'),
	ObjectDefineProperties: require('./2018/ObjectDefineProperties'),
	OrdinaryCreateFromConstructor: require('./2018/OrdinaryCreateFromConstructor'),
	OrdinaryDefineOwnProperty: require('./2018/OrdinaryDefineOwnProperty'),
	OrdinaryGetOwnProperty: require('./2018/OrdinaryGetOwnProperty'),
	OrdinaryGetPrototypeOf: require('./2018/OrdinaryGetPrototypeOf'),
	OrdinaryHasInstance: require('./2018/OrdinaryHasInstance'),
	OrdinaryHasProperty: require('./2018/OrdinaryHasProperty'),
	OrdinarySetPrototypeOf: require('./2018/OrdinarySetPrototypeOf'),
	OrdinaryToPrimitive: require('./2018/OrdinaryToPrimitive'),
	PromiseResolve: require('./2018/PromiseResolve'),
	QuoteJSONString: require('./2018/QuoteJSONString'),
	RawBytesToNumber: require('./2018/RawBytesToNumber'),
	RegExpCreate: require('./2018/RegExpCreate'),
	RegExpExec: require('./2018/RegExpExec'),
	RequireObjectCoercible: require('./2018/RequireObjectCoercible'),
	SameValue: require('./2018/SameValue'),
	SameValueNonNumber: require('./2018/SameValueNonNumber'),
	SameValueZero: require('./2018/SameValueZero'),
	SecFromTime: require('./2018/SecFromTime'),
	Set: require('./2018/Set'),
	SetFunctionLength: require('./2018/SetFunctionLength'),
	SetFunctionName: require('./2018/SetFunctionName'),
	SetIntegrityLevel: require('./2018/SetIntegrityLevel'),
	SetValueInBuffer: require('./2018/SetValueInBuffer'),
	SpeciesConstructor: require('./2018/SpeciesConstructor'),
	SplitMatch: require('./2018/SplitMatch'),
	StringCreate: require('./2018/StringCreate'),
	StringGetOwnProperty: require('./2018/StringGetOwnProperty'),
	SymbolDescriptiveString: require('./2018/SymbolDescriptiveString'),
	TestIntegrityLevel: require('./2018/TestIntegrityLevel'),
	thisBooleanValue: require('./2018/thisBooleanValue'),
	thisNumberValue: require('./2018/thisNumberValue'),
	thisStringValue: require('./2018/thisStringValue'),
	thisSymbolValue: require('./2018/thisSymbolValue'),
	thisTimeValue: require('./2018/thisTimeValue'),
	ThrowCompletion: require('./2018/ThrowCompletion'),
	TimeClip: require('./2018/TimeClip'),
	TimeFromYear: require('./2018/TimeFromYear'),
	TimeString: require('./2018/TimeString'),
	TimeWithinDay: require('./2018/TimeWithinDay'),
	TimeZoneString: require('./2018/TimeZoneString'),
	ToBoolean: require('./2018/ToBoolean'),
	ToDateString: require('./2018/ToDateString'),
	ToIndex: require('./2018/ToIndex'),
	ToInt16: require('./2018/ToInt16'),
	ToInt32: require('./2018/ToInt32'),
	ToInt8: require('./2018/ToInt8'),
	ToInteger: require('./2018/ToInteger'),
	ToLength: require('./2018/ToLength'),
	ToNumber: require('./2018/ToNumber'),
	ToObject: require('./2018/ToObject'),
	ToPrimitive: require('./2018/ToPrimitive'),
	ToPropertyDescriptor: require('./2018/ToPropertyDescriptor'),
	ToPropertyKey: require('./2018/ToPropertyKey'),
	ToString: require('./2018/ToString'),
	ToUint16: require('./2018/ToUint16'),
	ToUint32: require('./2018/ToUint32'),
	ToUint8: require('./2018/ToUint8'),
	ToUint8Clamp: require('./2018/ToUint8Clamp'),
	Type: require('./2018/Type'),
	TypedArrayCreate: require('./2018/TypedArrayCreate'),
	TypedArraySpeciesCreate: require('./2018/TypedArraySpeciesCreate'),
	UnicodeEscape: require('./2018/UnicodeEscape'),
	UTF16Decode: require('./2018/UTF16Decode'),
	UTF16Encoding: require('./2018/UTF16Encoding'),
	ValidateAndApplyPropertyDescriptor: require('./2018/ValidateAndApplyPropertyDescriptor'),
	ValidateAtomicAccess: require('./2018/ValidateAtomicAccess'),
	ValidateTypedArray: require('./2018/ValidateTypedArray'),
	WeekDay: require('./2018/WeekDay'),
	WordCharacters: require('./2018/WordCharacters'),
	YearFromTime: require('./2018/YearFromTime')
};

module.exports = ES2018;
